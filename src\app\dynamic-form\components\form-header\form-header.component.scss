@use '../../../../styles/shared.scss' as *;

.form-header {
  background-color: white;
  padding: 16px 0;
}

.horizontal-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
  padding: 0 24px;
  background-color: white;
}

.form-field {
  display: flex;
  align-items: center;
  
  p {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 18px;
    color: #283A97;
    margin: 0;
    padding: 8px 16px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    min-width: 120px;
    text-align: center;
  }
}

.button-group {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

/* Button Container with Rounded Corners */
.button-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  flex-wrap: nowrap;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

/* Enhanced Main Form Action Button Styles */
.form-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 2px solid;
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  margin: 0;
  min-width: 36px;
  min-height: 36px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  
  mat-icon {
    font-size: 16px !important;
    width: 16px !important;
    height: 16px !important;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1 !important;
    margin: 0;
    padding: 0;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

/* High specificity selectors to override global styles */
.form-action-button .mat-icon,
.form-action-button mat-icon.material-icons,
.form-action-button .mat-icon.material-icons {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
  line-height: 1 !important;
  min-width: 16px !important;
  min-height: 16px !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* Toggle View Button - Teal theme */
.form-action-button.toggle-view-button {
  border-color: #009688;
  background: linear-gradient(135deg, #009688 0%, #00796B 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #00796B 0%, #00695C 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 150, 136, 0.4);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 150, 136, 0.3);
  }
}

/* Submit Button - Green theme */
.form-action-button.submit-button {
  border-color: #4CAF50;
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
  }
}

/* Validate Button - Blue theme */
.form-action-button.validate-button {
  border-color: #2196F3;
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  color: white;

  &:hover {
    background: linear-gradient(135deg, #1976D2 0%, #1565C0 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(33, 150, 243, 0.3);
  }

  /* Make validate icon more prominent */
  mat-icon {
    font-size: 18px !important;
    width: 18px !important;
    height: 18px !important;
    font-weight: bold !important;
  }
}

/* Authorize Button - Purple theme */
.form-action-button.authorize-button {
  border-color: #9C27B0;
  background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #7B1FA2 0%, #6A1B9A 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(156, 39, 176, 0.4);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(156, 39, 176, 0.3);
  }
}

/* Back Button - Gray theme */
.form-action-button.back-button {
  border-color: #607D8B;
  background: linear-gradient(135deg, #607D8B 0%, #455A64 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #455A64 0%, #37474F 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(96, 125, 139, 0.4);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(96, 125, 139, 0.3);
  }
}

/* Reject Button - Red theme */
.form-action-button.reject-button {
  border-color: #F44336;
  background: linear-gradient(135deg, #F44336 0%, #D32F2F 100%);
  color: white;

  &:hover {
    background: linear-gradient(135deg, #D32F2F 0%, #C62828 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.4);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(244, 67, 54, 0.3);
  }

  /* Make reject icon more prominent */
  mat-icon {
    font-size: 18px !important;
    width: 18px !important;
    height: 18px !important;
    font-weight: bold !important;
  }
}

/* Delete Button - Dark Red theme */
.form-action-button.delete-button {
  border-color: #D32F2F;
  background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%);
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #B71C1C 0%, #8E0000 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(211, 47, 47, 0.4);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(211, 47, 47, 0.3);
  }
}

/* Error message styling */
.error-message {
  color: #f44336;
  font-size: 14px;
  font-weight: 500;
  padding: 8px 12px;
  background-color: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.3);
  border-radius: 4px;
  margin-left: 12px;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .form-action-button {
    width: 32px;
    height: 32px;
    min-width: 32px;
    min-height: 32px;
  }

  .form-action-button mat-icon,
  .form-action-button .mat-icon,
  .form-action-button mat-icon.material-icons,
  .form-action-button .mat-icon.material-icons {
    font-size: 14px !important;
    width: 14px !important;
    height: 14px !important;
    min-width: 14px !important;
    min-height: 14px !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  /* Keep validate and reject icons larger for visibility */
  .form-action-button.validate-button mat-icon,
  .form-action-button.reject-button mat-icon {
    font-size: 16px !important;
    width: 16px !important;
    height: 16px !important;
  }
}

@media (max-width: 768px) {
  .horizontal-container {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
    padding: 0 16px;
  }
  
  .form-field {
    justify-content: center;
    
    p {
      font-size: 16px;
      min-width: 100px;
    }
  }
  
  .button-group {
    justify-content: center;
    gap: 8px;
  }
  
  .button-container {
    padding: 6px 8px;
    gap: 6px;
    justify-content: center;
  }
  
  .form-action-button {
    width: 32px;
    height: 32px;
    min-width: 32px;
    min-height: 32px;

    mat-icon,
    .mat-icon,
    mat-icon.material-icons,
    .mat-icon.material-icons {
      font-size: 14px !important;
      width: 14px !important;
      height: 14px !important;
      min-width: 14px !important;
      min-height: 14px !important;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }

  /* Keep validate and reject icons larger for visibility */
  .form-action-button.validate-button mat-icon,
  .form-action-button.reject-button mat-icon {
    font-size: 16px !important;
    width: 16px !important;
    height: 16px !important;
  }
}

@media (max-width: 480px) {
  .button-group {
    gap: 6px;
  }
  
  .button-container {
    padding: 4px 6px;
    gap: 4px;
  }
  
  .form-action-button {
    width: 28px;
    height: 28px;
    min-width: 28px;
    min-height: 28px;

    mat-icon,
    .mat-icon,
    mat-icon.material-icons,
    .mat-icon.material-icons {
      font-size: 12px !important;
      width: 12px !important;
      height: 12px !important;
      min-width: 12px !important;
      min-height: 12px !important;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }

  /* Keep validate and reject icons larger for visibility */
  .form-action-button.validate-button mat-icon,
  .form-action-button.reject-button mat-icon {
    font-size: 14px !important;
    width: 14px !important;
    height: 14px !important;
  }
  
  .error-message {
    font-size: 12px;
    padding: 6px 8px;
    margin-left: 8px;
  }
}

@media (max-width: 360px) {
  .form-action-button {
    width: 24px;
    height: 24px;
    min-width: 24px;
    min-height: 24px;
  }

  .form-action-button mat-icon,
  .form-action-button .mat-icon,
  .form-action-button mat-icon.material-icons,
  .form-action-button .mat-icon.material-icons {
    font-size: 10px !important;
    width: 10px !important;
    height: 10px !important;
    min-width: 10px !important;
    min-height: 10px !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  /* Keep validate and reject icons larger for visibility */
  .form-action-button.validate-button mat-icon,
  .form-action-button.reject-button mat-icon {
    font-size: 12px !important;
    width: 12px !important;
    height: 12px !important;
  }
}

.readonly-button {
  pointer-events: none;
  opacity: 0.6;
  cursor: not-allowed;
}