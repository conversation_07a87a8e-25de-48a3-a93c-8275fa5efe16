{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"FADIA": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/fadia", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}, "src/assets"], "styles": ["@angular/material/prebuilt-themes/azure-blue.css", "src/styles.scss"], "scripts": []}, "configurations": {"production": {"optimization": {"scripts": true, "styles": true, "fonts": true}, "allowedCommonJsDependencies": ["canvg", "jspdf", "html2canvas", "core-js", "raf", "rgbcolor"], "budgets": [{"type": "initial", "maximumWarning": "1MB", "maximumError": "2MB"}], "outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.ts"}]}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}]}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "FADIA:build:production"}, "development": {"buildTarget": "FADIA:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}, "src/assets"], "styles": ["@angular/material/prebuilt-themes/azure-blue.css", "src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": "2f108506-5a50-491b-a166-34d1788d9d75"}}