import { inject, Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError, of } from 'rxjs';
import { map, catchError, switchMap, tap } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import {
  LoginCredentials,
  LoginResponse,
  UserProfile,
  SetPasswordData,
  AuthError
} from '../core/models/auth.models';

@Injectable({
  providedIn: 'root'
})
export class AuthenticationService {
  private readonly http = inject(HttpClient);

  // Centralized API configuration - using environment baseURL
  private readonly API_BASE = environment.baseURL;
  private readonly endpoints = {
    login: `${environment.baseURL}/auth/login`,
    profile: `${environment.baseURL}/api/signon-profile/`, // Keeping your original trailing slash
    logout: `${environment.baseURL}/auth/logout`,
    setPassword: `${environment.baseURL}/auth/set-password`
  } as const;

  private readonly httpOptions = {
    withCredentials: true
  } as const;

  /**
   * Login user with credentials
   */
  login(credentials: LoginCredentials): Observable<LoginResponse> {
    return this.http.post<LoginResponse>(this.endpoints.login, credentials, this.httpOptions)
      .pipe(
        catchError(this.handleError.bind(this))
      );
  }

  /**
   * Get user profile by username - EXACTLY matching your original URL structure
   */
  getProfile(username: string): Observable<UserProfile> {
    return this.http.get<UserProfile>(`${this.endpoints.profile}${username}`, this.httpOptions)
      .pipe(
        catchError(this.handleError.bind(this))
      );
  }

  /**
   * Login and automatically fetch profile - Sugar method for common workflow
   */
  loginWithProfile(credentials: LoginCredentials): Observable<{ response: LoginResponse; profile?: UserProfile }> {
    return this.login(credentials).pipe(
      switchMap(response => {
        if (response.success) {
          if (response['privileges']) {
         localStorage.setItem('userPrivileges',
          JSON.stringify(response['privileges']));
          localStorage.setItem('selectedBranch', JSON.stringify(response['privileges'][0]));
            }
          return this.getProfile(credentials.username).pipe(
            map(profile => ({ response, profile })),
            catchError(error => of({ response, profile: undefined, profileError: error }))
          );
        }
        return of({ response });
      })
    );
  }

  /**
   * Logout user
   */
  logout(): Observable<string> {
    return this.http.post(this.endpoints.logout, {}, {
      ...this.httpOptions,
      responseType: 'text'
    }).pipe(
      tap(() => this.clearLocalStorage()),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * Set password for user
   */
  setPassword(data: SetPasswordData): Observable<any> {
    return this.http.post(this.endpoints.setPassword, data)
      .pipe(
        catchError(this.handleError.bind(this))
      );
  }

  /**
   * Check if user is logged in (sugar method)
   */
  isLoggedIn(): boolean {
    return !!localStorage.getItem('profile');
  }

  /**
   * Get current user profile from localStorage (sugar method)
   */
  getCurrentUser(): UserProfile | null {
    const profile = localStorage.getItem('profile');
    return profile ? JSON.parse(profile) : null;
  }

  /**
   * Save user profile to localStorage (sugar method)
   */
  saveUserProfile(profile: UserProfile): void {
    localStorage.setItem('profile', JSON.stringify(profile));
  }

  /**
   * Clear all authentication data (sugar method)
   */
  clearLocalStorage(): void {
    localStorage.removeItem('profile');
    localStorage.removeItem('canAccessSetPassword');
    sessionStorage.removeItem('tempUsername');
  }

  /**
   * Get temporary username from session storage (sugar method)
   */
  getTempUsername(): string | null {
    return sessionStorage.getItem('tempUsername');
  }

  /**
   * Check if user can access set password page (sugar method)
   */
  canAccessSetPassword(): boolean {
    return localStorage.getItem('canAccessSetPassword') === 'true';
  }

  /**
   * Complete login workflow - handles all the common login steps (sugar method)
   */
  completeLogin(credentials: LoginCredentials, onSuccess: (profile: UserProfile) => void, onError: (error: AuthError) => void): void {
    this.loginWithProfile(credentials).subscribe({
      next: ({ response, profile }) => {
        if (response.success && profile) {
          this.saveUserProfile(profile);
          onSuccess(profile);
        } else {
          onError({
            type: 'UNKNOWN',
            message: response.message || 'Login failed'
          });
        }
      },
      error: onError
    });
  }

  /**
   * Enhanced error handling with typed errors
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    console.error('Authentication Error:', {
      status: error.status,
      statusText: error.statusText,
      error: error.error,
      url: error.url
    });

    let authError: AuthError;

    if (error.error?.message) {
      const message = error.error.message;

      if (message === 'No password set for this user') {
        authError = {
          type: 'NO_PASSWORD_SET',
          message,
          originalError: error
        };
      } else if (message.includes('Invalid') || message.includes('password')) {
        authError = {
          type: 'INVALID_CREDENTIALS',
          message,
          originalError: error
        };
      } else {
        authError = {
          type: 'UNKNOWN',
          message: `Server Error: ${message}`,
          originalError: error
        };
      }
    } else if (error.status === 0) {
      authError = {
        type: 'NETWORK_ERROR',
        message: 'Network error. Please check your connection and ensure the server is running.',
        originalError: error
      };
    } else if (error.status === 404) {
      authError = {
        type: 'UNKNOWN',
        message: 'Authentication service not found. Please check the API configuration.',
        originalError: error
      };
    } else if (error.status === 500) {
      authError = {
        type: 'UNKNOWN',
        message: 'Server error. Please try again later.',
        originalError: error
      };
    } else {
      authError = {
        type: 'UNKNOWN',
        message: `Unexpected error (${error.status}): ${error.statusText}`,
        originalError: error
      };
    }

    return throwError(() => authError);
  }
}
