@use './styles/shared.scss' as *;

/* You can add global styles to this file, and also import other style files */
@import '@angular/material/prebuilt-themes/azure-blue.css';

body {
  font-family: 'Poppins', sans-serif;
  color: #333;
  margin: 0;
  padding: 0;
}

/* Angular Material Styles - Using default theme */

/* Material Icons Font Preload and Fix */
@import url('https://fonts.googleapis.com/icon?family=Material+Icons&display=swap');

/* Ensure Material Icons are loaded immediately */
.material-icons {
  font-family: 'Material Icons' !important;
  font-weight: normal !important;
  font-style: normal !important;
  font-size: 24px !important;
  line-height: 1 !important;
  letter-spacing: normal !important;
  text-transform: none !important;
  display: inline-block !important;
  white-space: nowrap !important;
  word-wrap: normal !important;
  direction: ltr !important;
  -webkit-font-feature-settings: 'liga' !important;
  -webkit-font-smoothing: antialiased !important;
  text-rendering: optimizeLegibility !important;
  -moz-osx-font-smoothing: grayscale !important;
  font-feature-settings: 'liga' !important;
  /* Prevent icon shape changes */
  min-width: 1em !important;
  min-height: 1em !important;
  vertical-align: middle !important;
}

/* Fix for Material Icons in Angular Material components */
.mat-icon.material-icons {
  font-family: 'Material Icons' !important;
  font-weight: normal !important;
  font-style: normal !important;
  line-height: 1 !important;
  text-transform: none !important;
  letter-spacing: normal !important;
  word-wrap: normal !important;
  white-space: nowrap !important;
  direction: ltr !important;
  -webkit-font-smoothing: antialiased !important;
  text-rendering: optimizeLegibility !important;
  -moz-osx-font-smoothing: grayscale !important;
  font-feature-settings: 'liga' !important;
  -webkit-font-feature-settings: 'liga' !important;
  /* Prevent layout shifts */
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Global Styles */
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: Roboto, "Helvetica Neue", sans-serif;
}

* {
  box-sizing: border-box;
}

// Custom CSS variables for consistent theming
:root {
  --primary-color: #2196f3;
  --primary-dark: #1976d2;
  --accent-color: #4caf50;
  --accent-dark: #388e3c;
  --warn-color: #f44336;
  --background-color: #fafafa;
  --surface-color: #ffffff;
  --text-primary: rgba(0, 0, 0, 0.87);
  --text-secondary: rgba(0, 0, 0, 0.6);
  --divider-color: rgba(0, 0, 0, 0.12);
}

// Smooth transitions for all interactive elements
* {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

// Remove default button styles
button {
  border: none;
  outline: none;
  cursor: pointer;
}

// Accessibility improvements
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// Focus styles for accessibility
*:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

// menu gloabal styls .scss-------------------------------------------------->

/* General Container Styles */
.home-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Toolbar Styles with White to Blue Gradient */
.compact-toolbar {
  height: 72px;
  padding: 0 10px;
  font-size: 14px;
  display: flex;
  align-items: center;
  color: #ffffff; 
  //background: linear-gradient(to left, #2d4f86, rgba(255, 255, 255, 0.8)); 
}

/* Logo Styles */
.logo {
  height: 30px;
  margin-left: 10px;
  vertical-align: middle;
}

/* Spacer for Right Alignment */
.spacer {
  flex: 1 1 auto;
}

/* Menu Icon Animation */
.menu-icon {
  .mat-icon {
    transition: transform 0.3s ease;
    color: #00a651; 
  }

  &.rotated {
    transform: rotate(90deg);
  }
}

/* Sidenav Styles */
.compact-sidenav-container {
  flex: 1;
}

.compact-sidenav {
  width: 220px;
  padding: 10px;
 // background-color: #f5f5f5;
  background-color: #FAFAFA;
  color: #2d4f86;
}

/* Sidebar Menu Item Styles */
.compact-menu-item {
  font-size: 13px;
  margin: 5px 0;
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s, color 0.2s;
  color: #2d4f86; 

  &:hover {
    background-color: #e0e0e0;
    color: #00a651; 
  }

  &.active {
    background-color: #4a90e2; 
    color: #ffffff;
  }
}

.mat-icon {
  margin-right: 8px;
}

/* Sidebar Submenu Item Styles */
.compact-submenu-item {
  font-size: 13px;
  margin: 5px 0;
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px;
  padding-left: 20px; /* Indentation for submenu items */
  border-radius: 6px;
  transition: background-color 0.2s, color 0.2s;
  color: #2d4f86; 

  &:hover {
    background-color: #e0e0e0;
    color: #00a651; 
  }

  &.active {
    background-color: #4a90e2; 
    color: #ffffff;
  }
}

/* Professional Tab Navigation with Arrow Controls */
.tab-container {
  display: flex;
  align-items: center;
  background: #ffffff;
  padding: 8px 16px 0px;
  height: 50px; /* Increased height for better visibility */
  border-bottom: 1px solid #0DB14B;
  position: relative;
}

.tab-scroll-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
  flex-shrink: 0;

  &:hover:not(.disabled) {
    background: #e9ecef;
    border-color: #3B82F6;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
  }

  &.disabled {
    opacity: 0.4;
    cursor: not-allowed;
    background: #f8f9fa;
  }

  mat-icon {
    font-size: 18px;
    color: #495057;
  }

  &:hover:not(.disabled) mat-icon {
    color: #3B82F6;
  }
}

.tab-scroll-left {
  margin-right: 8px;
}

.tab-scroll-right {
  margin-left: 8px;
}

.tab-scroll-wrapper {
  flex: 1;
  overflow-x: auto;
  overflow-y: hidden;
  position: relative;
  scroll-behavior: smooth;

  /* Hide scrollbar but keep functionality */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */

  &::-webkit-scrollbar {
    display: none; /* WebKit */
  }
}

.nav-tabs {
  display: flex;
  padding: 0;
  margin: 0;
  list-style: none;
  height: 32px;
  white-space: nowrap;
}

.nav-item {
  flex-shrink: 0; /* Prevent tabs from shrinking */
  margin-right: 4px;

  &:last-child {
    margin-right: 0;
  }
}

.nav-link {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding: 6px 16px;
  gap: 8px;
  border-radius: 12px 12px 0px 0px;
  text-decoration: none;
  color: #222222;
  background: #FAFAFA;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 21px;
  position: relative;
  cursor: pointer;
  white-space: nowrap;
  min-width: 120px; /* Ensure minimum tab width */
  transition: all 0.3s ease;
  border: 1px solid transparent;

  &:hover {
    background: #f0f0f0;
    border-color: #e0e0e0;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.nav-item.active .nav-link {
  background: #ffffff;
  border: 1px solid #0DB14B;
  border-bottom: none;
  color: #222222;
  box-shadow: 0 -2px 8px rgba(13, 177, 75, 0.1);
  z-index: 2;

  &:hover {
    transform: none; /* Remove hover transform for active tab */
  }
}

.close-tab {
  background: none;
  border: none;
  font-size: 18px;
  line-height: 1;
  cursor: pointer;
  color: #666;
  margin-left: 4px;
  padding: 2px;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  opacity: 0.7;

  &:hover {
    opacity: 1;
    background: rgba(255, 0, 0, 0.1);
    color: #dc3545;
  }
}

/* Tab Content Area with Animation */
.tab-content {
  padding: 0px; 
  animation: fadeIn 0.3s ease-in-out; 
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Responsive Icon Styles */
.responsive-icon {
  width: 100%;
  height: 100%;
  max-width: 24px;
  max-height: 24px;
  object-fit: contain;
  transition: all 0.3s ease;
}

.responsive-icon-frame {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

/* Button Icon Responsive Sizing */
.button-icon-small {
  width: 16px;
  height: 16px;
}

.button-icon-medium {
  width: 20px;
  height: 20px;
}

.button-icon-large {
  width: 24px;
  height: 24px;
}

/* Responsive Styles for Small Screens */
@media (max-width: 768px) {
  .compact-nav {
    display: none;
  }

  .compact-toolbar {
    justify-content: space-between;
    margin-bottom: 0; /* Remove any default margin on the toolbar */
  }

  .compact-tab-button {
    font-size: 12px;
    padding: 4px 8px;
  }

  .compact-menu-item {
    font-size: 10px;
    padding: 4px;
  }

  /* Responsive tab navigation for mobile */
  .tab-container {
    height: 45px;
    padding: 6px 12px 0px;
  }

  .tab-scroll-arrow {
    width: 28px;
    height: 28px;

    mat-icon {
      font-size: 16px;
    }
  }

  .nav-link {
    height: 28px;
    padding: 4px 12px;
    font-size: 12px;
    min-width: 100px;
    gap: 6px;
  }

  .close-tab {
    width: 18px;
    height: 18px;
    font-size: 16px;
  }

  /* Responsive icon sizing for mobile */
  .responsive-icon-frame {
    width: 20px;
    height: 20px;
  }

  .button-icon-small {
    width: 14px;
    height: 14px;
  }

  .button-icon-medium {
    width: 18px;
    height: 18px;
  }

  .button-icon-large {
    width: 20px;
    height: 20px;
  }
}

/* Responsive Styles for Extra Small Screens */
@media (max-width: 480px) {
  .tab-container {
    height: 42px;
    padding: 4px 8px 0px;
  }

  .tab-scroll-arrow {
    width: 24px;
    height: 24px;

    mat-icon {
      font-size: 14px;
    }
  }

  .nav-link {
    height: 26px;
    padding: 3px 8px;
    font-size: 11px;
    min-width: 80px;
    gap: 4px;
  }

  .close-tab {
    width: 16px;
    height: 16px;
    font-size: 14px;
  }

  .responsive-icon-frame {
    width: 18px;
    height: 18px;
  }

  .button-icon-small {
    width: 12px;
    height: 12px;
  }

  .button-icon-medium {
    width: 16px;
    height: 16px;
  }

  .button-icon-large {
    width: 18px;
    height: 18px;
  }
}

/* Responsive Styles for Large Screens */
@media (min-width: 1200px) {
  .tab-container {
    height: 55px;
    padding: 10px 20px 0px;
  }

  .tab-scroll-arrow {
    width: 36px;
    height: 36px;

    mat-icon {
      font-size: 20px;
    }
  }

  .nav-link {
    height: 36px;
    padding: 8px 20px;
    font-size: 15px;
    min-width: 140px;
    gap: 10px;
  }

  .close-tab {
    width: 22px;
    height: 22px;
    font-size: 20px;
  }

  .responsive-icon-frame {
    width: 28px;
    height: 28px;
  }

  .button-icon-small {
    width: 18px;
    height: 18px;
  }

  .button-icon-medium {
    width: 22px;
    height: 22px;
  }

  .button-icon-large {
    width: 26px;
    height: 26px;
  }
}

/* Logout Button */
.logout-button {
  color: #0a0a0a !important; /* Override any default button color */
  font-weight: bold;
  transition: color 0.3s;

  &:hover {
    color: #4a90e2; 
  }
}

.search-bar {
  margin-right: 20px;
  width: 200px;
}

/* Tab text styling */
.tab-text {
  white-space: nowrap;
  flex: 1;
  text-align: center;
}

//----------------------------------------------

/* Global Responsive Table Utilities */
.responsive-table-container {
  width: 100%;
  overflow-x: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  // Custom scrollbar
  &::-webkit-scrollbar {
    height: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
    
    &:hover {
      background: #a8a8a8;
    }
  }
}

.responsive-table {
  width: 100%;
  min-width: 600px; // Minimum width to prevent cramping
  border-collapse: collapse;
  
  th, td {
    padding: 12px 8px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
  }
  
  th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    position: sticky;
    top: 0;
    z-index: 10;
  }
  
  tbody tr {
    &:nth-child(even) {
      background-color: #f8f9fa;
    }
    
    &:hover {
      background-color: #e9ecef;
      cursor: pointer;
    }
  }
  
  // Mobile responsive adjustments
  @media (max-width: 768px) {
    min-width: 500px;
    font-size: 12px;
    
    th, td {
      padding: 8px 4px;
      max-width: 120px;
    }
  }
  
  @media (max-width: 480px) {
    min-width: 400px;
    font-size: 11px;
    
    th, td {
      padding: 6px 2px;
      max-width: 100px;
    }
  }
}

// Material Table responsive enhancements
.mat-table.responsive-mat-table {
  width: 100%;
  
  .mat-header-cell {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
    position: sticky;
    top: 0;
    z-index: 10;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .mat-cell {
    border-bottom: 1px solid #dee2e6;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
  }
  
  .mat-row {
    &:nth-child(even) {
      background-color: #f8f9fa;
    }
    
    &:hover {
      background-color: #e9ecef;
      cursor: pointer;
    }
  }
  
  @media (max-width: 768px) {
    font-size: 12px;
    
    .mat-header-cell,
    .mat-cell {
      padding: 8px 4px;
      max-width: 120px;
    }
  }
  
  @media (max-width: 480px) {
    font-size: 11px;
    
    .mat-header-cell,
    .mat-cell {
      padding: 6px 2px;
      max-width: 100px;
    }
  }
}

// Responsive card containers
.responsive-card {
  margin: 16px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  
  @media (max-width: 768px) {
    margin: 12px;
  }
  
  @media (max-width: 480px) {
    margin: 8px;
    border-radius: 8px;
  }
}

// Responsive button groups
.responsive-button-group {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  
  @media (max-width: 768px) {
    gap: 8px;
  }
  
  @media (max-width: 480px) {
    flex-direction: column;
    gap: 8px;
    
    button {
      width: 100%;
      justify-content: center;
    }
  }
}

// Responsive form fields
.responsive-form-field {
  width: 100%;
  margin-bottom: 16px;
  
  @media (max-width: 768px) {
    margin-bottom: 12px;
  }
  
  @media (max-width: 480px) {
    margin-bottom: 8px;
  }
}

//----------------------------------------------


.icon-frame img {
  width: 32px;
  height: 32px;
  object-fit: cover;
  border-radius: 50%;
  padding-right: 10px;
  transition: all 0.3s ease;
}

/* Responsive sizing for header icons */
@media (max-width: 768px) {
  .icon-frame img {
    width: 28px;
    height: 28px;
    padding-right: 8px;
  }
}

@media (max-width: 480px) {
  .icon-frame img {
    width: 24px;
    height: 24px;
    padding-right: 6px;
  }
}


.username {
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 21px;
  color: #222222;
  display: flex;
  align-items: center;
  height: 21px;
  width: 116px;
}

.company {
  font-family: 'Poppins', sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 18px;
  color: #4E4E4E;
  flex-grow: 1;
}

//------------------------------

.submenu-arrow {
  transition: transform 0.3s ease;
}
.submenu-arrow.rotated {
  transform: rotate(180deg);
}


.compact-menu-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  gap: 12px;
  cursor: pointer;
}

.icon-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  background-color: #E7F7ED; /* خلفية خفيفة */
  border-radius: 50%;
  flex-shrink: 0;
  color: #0DB14B;
  transition: all 0.3s ease;
}

.icon-wrapper mat-icon {
  color: #0DB14B; /* لون الأيقونة أخضر */
  font-size: 20px;
  transition: all 0.3s ease;
}

/* Responsive sidebar icon sizing */
@media (max-width: 768px) {
  .icon-wrapper {
    width: 28px;
    height: 28px;
  }

  .icon-wrapper mat-icon {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .icon-wrapper {
    width: 24px;
    height: 24px;
  }

  .icon-wrapper mat-icon {
    font-size: 16px;
  }
}

@media (min-width: 1200px) {
  .icon-wrapper {
    width: 36px;
    height: 36px;
  }

  .icon-wrapper mat-icon {
    font-size: 22px;
  }
}

.text-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.application-name {
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #222222;
}

.company-name {
  font-family: 'Poppins', sans-serif;
  font-weight: 400;
  font-size: 12px;
  color: #4E4E4E;
}

.submenu-arrow {
  margin-left: auto;
  transition: transform 0.3s ease;
}

.submenu-arrow.rotated {
  transform: rotate(180deg);
}

.submenu-item {
  background-color: #ffffff;
  border-radius: 8px;
}



.clickable {
  cursor: pointer;
  color: #90caf9;
  font-weight: bold;
}

.clickable:hover {
  text-decoration: underline;
}