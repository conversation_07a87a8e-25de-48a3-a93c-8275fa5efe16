import { Component, inject, OnInit } from '@angular/core';
import { AuthenticationService } from '../services/authentication.service';
import { NavigationService } from '../services/navigation.service';
import { Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, AbstractControl } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

@Component({
  selector: 'app-set-password',
  imports: [
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './set-password.component.html',
  styleUrl: './set-password.component.scss'
})
export class SetPasswordComponent implements OnInit {
  setPasswordForm!: FormGroup;
  errorMessage: string = '';
  successMessage: string = '';
  username: string = '';
  isLoading: boolean = false;
  hideNewPassword: boolean = true;
  hideConfirmPassword: boolean = true;

  private authenticationService = inject(AuthenticationService);
  private navigationService = inject(NavigationService);
  private router = inject(Router);
  private formBuilder = inject(FormBuilder);

  constructor() {
    this.initializeForm();
  }

  ngOnInit(): void {
    this.username = sessionStorage.getItem('tempUsername') || '';
    sessionStorage.removeItem('tempUsername');
  }

  private initializeForm(): void {
    this.setPasswordForm = this.formBuilder.group({
      newPassword: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]]
    }, { validators: this.passwordMatchValidator });
  }

  // Custom validator to check if passwords match
  private passwordMatchValidator(control: AbstractControl): { [key: string]: any } | null {
    const newPassword = control.get('newPassword');
    const confirmPassword = control.get('confirmPassword');

    if (newPassword && confirmPassword && newPassword.value !== confirmPassword.value) {
      return { passwordMismatch: true };
    }
    return null;
  }

  // Toggle password visibility for new password field
  toggleNewPasswordVisibility(): void {
    this.hideNewPassword = !this.hideNewPassword;
  }

  // Toggle password visibility for confirm password field
  toggleConfirmPasswordVisibility(): void {
    this.hideConfirmPassword = !this.hideConfirmPassword;
  }

  // Get error messages for form fields
  getErrorMessage(fieldName: string): string {
    const field = this.setPasswordForm.get(fieldName);

    if (field?.hasError('required')) {
      return `${fieldName === 'newPassword' ? 'New password' : 'Confirm password'} is required`;
    }

    if (field?.hasError('minlength')) {
      return 'Password must be at least 6 characters long';
    }

    return '';
  }

  onSubmit(): void {
    this.errorMessage = '';
    this.successMessage = '';

    // Check if form is valid
    if (this.setPasswordForm.invalid) {
      this.setPasswordForm.markAllAsTouched();
      return;
    }

    // Check for password mismatch (handled by custom validator)
    if (this.setPasswordForm.hasError('passwordMismatch')) {
      this.errorMessage = 'Passwords do not match.';
      return;
    }

    this.isLoading = true;

    const formValues = this.setPasswordForm.value;
    const requestData = {
      username: this.username,
      password: formValues.newPassword,
      confirmPassword: formValues.confirmPassword
    };

    this.authenticationService.setPassword(requestData).subscribe({
      next: (response) => {
        this.isLoading = false;
        if (response.success) {
          this.successMessage = response.message || 'Password has been set successfully!';

          // Show success message for 2 seconds then navigate
          setTimeout(() => {
            this.navigationService.navigateToLogin();
          }, 2000);
        } else {
          this.errorMessage = response.message || 'Failed to set password. Please try again.';
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.errorMessage = error.error?.message || 'An error occurred. Please try again later.';
        console.error('Set password failed:', error);
      }
    });
  }
}
