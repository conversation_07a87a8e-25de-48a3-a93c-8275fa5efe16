import { Component, Input, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import { environment } from '../../environments/environment';

import { FilterGroupComponent } from '../filter-group/filter-group.component'
@Component({
  selector: 'app-dynamic-query',
  templateUrl: './dynamic-query.component.html',
  styleUrls: ['./dynamic-query.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatSelectModule,
    MatOptionModule,
    MatTableModule,
    FilterGroupComponent,
    MatIconModule,
  ]
})
export class DynamicQueryComponent implements OnInit {
  @Input() queryName!: string;
  
  criteriaFields: any[] = [];
  selectFields: any[] = [];
  filterGroups: any[] = [];
  filterExpression: string = '';
  results: any[] = [];
  columns: string[] = [];
  showTable: boolean = false;

  constructor(private http: HttpClient) {}

  ngOnInit() {
    if (this.queryName) {
      this.fetchCriteriaFields();
    }
  }

  fetchCriteriaFields() {
    this.http.get<any>(`${environment.baseURL}/api/query-builder/${this.queryName}`, { withCredentials: true }).subscribe(
      (response: any) => {
        this.criteriaFields = response.formattedFields;
        this.selectFields = response._select;
        // If there are no filter groups yet, add one.
        // Subsequent calls to fetchCriteriaFields (e.g. if queryName changes) shouldn't re-add groups if some exist.
        if (this.filterGroups.length === 0) {
            this.addGroup();
        }
      },
      (error) => {
        console.error('Error fetching query criteria:', error);
      }
    );
  }

  private _getBaseFieldName(selectionFieldValue: string): string {
    if (!selectionFieldValue) return '';
    return selectionFieldValue.split(':')[0];
  }

  private _determineFieldType(selectionFieldValue: string): string {
    if (!selectionFieldValue) return 'string';
    const parts = selectionFieldValue.split(':');
    let determinedType = 'string'; // Default to string

    if (parts.length > 1) {
        const typePart = parts[parts.length - 1];
        if (typePart.endsWith('[]')) {
            // For array types like "CustomType[]", treat as string for generic operator compatibility
            determinedType = 'string';
        } else {
            const simpleType = typePart.toLowerCase();
            if (['int', 'double', 'date', 'boolean', 'string'].includes(simpleType)) {
                determinedType = simpleType;
            } else {
                determinedType = 'string'; // Default for unknown simple types
            }
        }
    } else if (selectionFieldValue.endsWith('[]')) {
        // Field like "maritalStatus[]"
        determinedType = 'string';
    }
    // else: simple field name like "customer", defaults to 'string'

    // Ensure it's one of the known types for operators
    if (!['int', 'double', 'date', 'boolean', 'string'].includes(determinedType)) {
        return 'string';
    }
    return determinedType;
  }

  addGroup() {
    this.filterGroups.push({
      logic: 'AND',
      logicToPrevious: this.filterGroups.length > 0 ? 'AND' : null,
      conditions: [], // Will be populated by addCondition
      children: []
    });
    // Add a default first condition to the new group and fetch its operators
    if (this.filterGroups[this.filterGroups.length - 1].conditions.length === 0) {
        this.addCondition(this.filterGroups.length - 1);
    }
  }

  removeGroup(index: number) {
    this.filterGroups.splice(index, 1);
    this.onGroupChange();
  }

  addCondition(groupIndex: number) {
    const newCondition = this.createEmptyCondition();
    this.filterGroups[groupIndex].conditions.push(newCondition);
    this.fetchOperatorsForCondition(newCondition); // Fetch operators for the default field
  }

  removeCondition(groupIndex: number, conditionIndex: number) {
    this.filterGroups[groupIndex].conditions.splice(conditionIndex, 1);
  }

  createEmptyCondition() {
    const firstField = this.criteriaFields[0];
    return {
      field: firstField?.selectionField || '',
      operator: '', // Will be set after operators are fetched
      value: '',
      availableOperators: []
    };
  }

  fetchOperatorsForCondition(condition: any) {
    if (!condition || !condition.field) {
      condition.availableOperators = [];
      condition.operator = '';
      this.onGroupChange();
      return;
    }

    const fieldType = this._determineFieldType(condition.field);
    const endpoint = `${environment.baseURL}/api/query-builder/search?queryBuilderId=uqlOperators`;
    const body = { fieldType: fieldType }; // Assumed body structure

    this.http.post<any[]>(endpoint, body, { withCredentials: true }).subscribe(
      (allOperatorsFromServer) => { // Renamed for clarity, was 'operators'
        // Filter operators based on fieldType and supportedFieldType
        const filteredOperators = allOperatorsFromServer.filter(op =>
          op.supportedFieldType && Array.isArray(op.supportedFieldType) && op.supportedFieldType.includes(fieldType)
        );
        condition.availableOperators = filteredOperators; // Store filtered operator objects

        if (condition.availableOperators && condition.availableOperators.length > 0) {
          // Set to first operator if current is invalid or not set, or if not in the new filtered list
          const currentOperatorIsValid = condition.operator &&
                                       condition.availableOperators.some((op: any) => op.ID === condition.operator);
          if (!currentOperatorIsValid) {
            condition.operator = condition.availableOperators[0].ID;
          }
        } else {
          condition.operator = ''; // No operators available
        }
        this.onGroupChange();
      },
      (error) => {
        console.error(`Error fetching operators for field type ${fieldType}:`, error);
        condition.availableOperators = [];
        condition.operator = '';
        // Optionally, provide fallback static operators based on type
        // For example:
        // if (fieldType === 'string') condition.availableOperators = [{ID: 'EQ', description: 'Equals'}, {ID: 'CT', description: 'Contains'}];
        // else if (fieldType === 'number') condition.availableOperators = [{ID: 'EQ', description: 'Equals'}, {ID: 'GT', description: 'Greater Than'}];
        // if (condition.availableOperators.length > 0) condition.operator = condition.availableOperators[0].ID;
        this.onGroupChange();
      }
    );
  }

  handleConditionFieldChange(condition: any) {
    // This method is called from the template when FilterGroupComponent emits an event
    this.fetchOperatorsForCondition(condition);
  }

  getInputType(fieldName: string): string {
    const field = this.criteriaFields.find(f => f.selectionField === fieldName);
    switch (field?.type) {
      case 'number': return 'number';
      case 'date': return 'date';
      default: return 'text';
    }
  }

  buildFilterExpression() {
    const buildGroupExpression = (group: any): string => {
      const expressions: string[] = [];

      if (group.conditions) {
        const conditionExprs = group.conditions
          .filter((cond: any) => cond.value !== '')
          .map((cond: any) => `${cond.field} ${cond.operator} "${cond.value}"`);

        expressions.push(...conditionExprs);
      }

      if (group.children && group.children.length > 0) {
        const childExprs = group.children.map((child: any) => buildGroupExpression(child));
        expressions.push(...childExprs);
      }

      return expressions.length ? `(${expressions.join(` ${group.logic} `)})` : '';
    };

    const finalParts: string[] = [];

    this.filterGroups.forEach((group: any, index: number) => {
      const groupExpr = buildGroupExpression(group);
      if (groupExpr) {
        if (index > 0 && group.logicToPrevious) {
          finalParts.push(group.logicToPrevious);
        }
        finalParts.push(groupExpr);
      }
    });

    this.filterExpression = finalParts.join(' ');
  }

  buildJsonFilterExpression(): any {
    const buildGroup = (group: any): any => {
      const expressions: any[] = [];

      // Add conditions
      for (const cond of group.conditions || []) {
        if (cond.value !== '') {
          const baseFieldName = this._getBaseFieldName(cond.field);
          expressions.push({
            [baseFieldName]: {
              [cond.operator]: cond.value
            }
          });
        }
      }

      // Add child groups recursively
      for (const child of group.children || []) {
        const childExpr = buildGroup(child);
        if (childExpr) {
          expressions.push(childExpr);
        }
      }

      if (expressions.length === 0) return null;
      if (expressions.length === 1) return expressions[0];

      return {
        [group.logic]: expressions
      };
    };

    const topGroups = this.filterGroups.map(buildGroup).filter(e => e != null);
    if (topGroups.length === 1) return topGroups[0];
    return { AND: topGroups };
  }

  submitQuery() {
    const url = `${environment.baseURL}/api/query-builder/search?queryBuilderId=${this.queryName}`;

    //const _select = this.criteriaFields.map(field => this._getBaseFieldName(field.selectionField));
    const _select = this.selectFields;
    const AND_or_fullExpression = this.buildJsonFilterExpression();

    const payload = {
      ...AND_or_fullExpression,
      _select
    };

    this.http.post<any[]>(url, payload, { withCredentials: true }).subscribe(
      (response) => {
        this.results = response;
        if (this.results.length > 0) {
          this.columns = Object.keys(this.results[0]);
          this.showTable = true;
        }
      },
      (error) => {
        console.error('Error fetching query results:', error);
      }
    );
  }

  downloadPDF() {
    const doc = new jsPDF();
    doc.text('Query Results', 14, 10);
    (doc as any).autoTable({
      head: [this.columns],
      body: this.results.map(row => this.columns.map(col => row[col])),
      startY: 20,
      theme: 'grid'
    });
    doc.save('query-results.pdf');
  }

  onGroupChange() {
    this.buildFilterExpression();
  }

  onRowClick(row: any) {
    // Handle row click - you can customize this based on your needs
    console.log('Row clicked:', row);
    // Example: Open a dialog, navigate to detail view, etc.
  }
}