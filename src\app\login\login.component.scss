// Global scroll behavior for smooth scrolling
:host {
  scroll-behavior: smooth;
}

// Ensure proper scrolling on mobile devices
* {
  -webkit-overflow-scrolling: touch;
}

// Fully responsive split-screen login container with scroll support
.login-container {
  display: grid;
  grid-template-columns: 3fr 2fr;
  min-height: 100vh;
  height: auto;
  overflow: visible;
  position: relative;
  scroll-behavior: smooth;

  // Large screens (1200px+)
  @media (min-width: 1200px) {
    grid-template-columns: 3fr 2fr;
    height: 100vh;
    overflow: hidden;
  }

  // Medium screens (769px - 1199px)
  @media (max-width: 1199px) and (min-width: 769px) {
    grid-template-columns: 1fr 1fr;
    height: 100vh;
    overflow: hidden;
  }

  // Tablets (481px - 768px)
  @media (max-width: 768px) and (min-width: 481px) {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto;
    height: auto;
    min-height: 100vh;
    overflow-y: auto;
  }

  // Mobile (361px - 480px)
  @media (max-width: 480px) and (min-width: 361px) {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto;
    height: auto;
    min-height: 100vh;
    overflow-y: auto;
  }

  // Professional subtle pattern
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 80%, rgba(30, 58, 138, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(30, 64, 175, 0.02) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
  }
}

// Left side - Responsive with content visibility support
.left-side {
  position: relative;
  background: linear-gradient(135deg,
    #1e3a8a 0%,
    #1e40af 50%,
    #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  padding: 2rem;

  // Large screens - Fixed height
  @media (min-width: 1200px) {
    padding: 2.5rem;
    min-height: 100vh;
  }

  // Medium screens - Fixed height
  @media (max-width: 1199px) and (min-width: 769px) {
    padding: 2rem;
    min-height: 100vh;
  }

  // Tablets - Auto height with minimum
  @media (max-width: 768px) and (min-width: 481px) {
    min-height: 50vh;
    padding: 1.5rem;
    overflow: visible;
  }

  // Mobile - Auto height with minimum
  @media (max-width: 480px) and (min-width: 361px) {
    min-height: 45vh;
    padding: 1rem;
    overflow: visible;
  }

  // Small mobile - Auto height with minimum
  @media (max-width: 360px) {
    min-height: 40vh;
    padding: 0.75rem;
    overflow: visible;
  }

  // Professional overlay
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    z-index: 1;
  }

  // Minimal professional background elements - optimized for faster loading
  .animated-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    // Defer animations until after initial render
    animation-play-state: paused;

    &.loaded {
      animation-play-state: running;
    }

    .floating-element {
      position: absolute;
      opacity: 0.4;
      transition: opacity 0.3s ease;
      will-change: opacity, transform;

      &:hover {
        opacity: 0.8;
      }

      // Simplified element hiding for mobile
      @media (max-width: 768px) {
        &.square-2,
        &.dot-3,
        &.triangle-2 {
          display: none;
        }
      }

      @media (max-width: 480px) {
        &.square-2,
        &.dot-3,
        &.triangle-2,
        &.plus-2,
        &.circle-3,
        &.arrow-2 {
          display: none;
        }
      }

      @media (max-width: 360px) {
        opacity: 0.3;

        &.square-2,
        &.dot-3,
        &.triangle-2,
        &.plus-2,
        &.circle-3,
        &.arrow-2,
        &.line-2,
        &.square-1 {
          display: none;
        }
      }

      &.circle {
        border: 3px solid rgba(255, 255, 255, 0.8);
        border-radius: 50%;
        animation: obviousPulse 4s infinite ease-in-out;
        box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);

        &.circle-1 {
          width: 100px;
          height: 100px;
          top: 15%;
          left: 20%;
          animation-delay: 0s;
          animation: obviousPulse 4s infinite ease-in-out, floatObvious 6s infinite ease-in-out;
        }

        &.circle-2 {
          width: 120px;
          height: 120px;
          top: 65%;
          right: 25%;
          animation-delay: 1.5s;
          animation: obviousPulse 4s infinite ease-in-out 1.5s, floatObvious 8s infinite ease-in-out 1.5s;
        }

        &.circle-3 {
          width: 80px;
          height: 80px;
          bottom: 25%;
          left: 15%;
          animation-delay: 3s;
          animation: obviousPulse 4s infinite ease-in-out 3s, floatObvious 7s infinite ease-in-out 3s;
        }
      }

      &.square {
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.4));
        transform: rotate(45deg);
        animation: spinObvious 8s linear infinite;
        box-shadow: 0 0 15px rgba(255, 255, 255, 0.2);
        border-radius: 4px;

        &.square-1 {
          width: 60px;
          height: 60px;
          top: 30%;
          right: 15%;
          animation-delay: 0s;
          animation: spinObvious 8s linear infinite, floatObvious 5s infinite ease-in-out;
        }

        &.square-2 {
          width: 45px;
          height: 45px;
          bottom: 40%;
          right: 30%;
          animation-delay: 2s;
          animation: spinObvious 8s linear infinite 2s, floatObvious 6s infinite ease-in-out 2s;
        }
      }

      &.dot {
        width: 12px;
        height: 12px;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 50%;
        animation: bounceObvious 3s infinite ease-in-out;
        box-shadow: 0 0 10px rgba(255, 255, 255, 0.4);

        &.dot-1 {
          top: 20%;
          right: 40%;
          animation-delay: 0s;
        }

        &.dot-2 {
          top: 70%;
          left: 30%;
          animation-delay: 1s;
        }

        &.dot-3 {
          bottom: 30%;
          right: 10%;
          animation-delay: 2s;
        }
      }

      &.arrow {
        color: rgba(255, 255, 255, 0.8);
        font-size: 28px;
        font-weight: bold;
        animation: fadeObvious 4s infinite;
        text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);

        &.arrow-1 {
          top: 40%;
          left: 20%;
          animation-delay: 0s;
        }

        &.arrow-2 {
          bottom: 50%;
          right: 25%;
          animation-delay: 2s;
        }
      }

      &.line {
        background: rgba(255, 255, 255, 0.7);
        height: 3px;
        animation: fadeObvious 3s infinite;
        box-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
        border-radius: 2px;

        &.line-1 {
          width: 80px;
          top: 25%;
          left: 40%;
          animation-delay: 0s;
        }

        &.line-2 {
          width: 60px;
          top: 55%;
          left: 25%;
          animation-delay: 1s;
        }
      }

      &.triangle {
        color: rgba(255, 255, 255, 0.7);
        font-size: 24px;
        animation: bounceObvious 5s infinite ease-in-out;
        text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);

        &.triangle-1 {
          top: 15%;
          right: 50%;
          animation-delay: 0.5s;
        }

        &.triangle-2 {
          bottom: 25%;
          left: 40%;
          animation-delay: 2.5s;
        }
      }

      &.plus {
        color: rgba(255, 255, 255, 0.7);
        font-size: 32px;
        font-weight: bold;
        animation: spinObvious 10s linear infinite;
        text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);

        &.plus-1 {
          top: 45%;
          left: 5%;
          animation-delay: 1s;
        }

        &.plus-2 {
          bottom: 15%;
          right: 45%;
          animation-delay: 3s;
        }
      }
    }
  }

  // Fully responsive professional welcome text
  .welcome-text {
    position: relative;
    z-index: 3;
    text-align: center;
    color: white;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    padding: 0 1rem;
    max-width: 100%;

    .hello {
      font-weight: 300;
      font-size: 3.5rem;
      margin: 0;
      line-height: 1.1;
      animation: fadeInUp 1s ease-out;
      color: rgba(255, 255, 255, 0.98);
      letter-spacing: 0.5px;

      // Large screens
      @media (min-width: 1200px) {
        font-size: 4rem;
      }

      // Medium screens
      @media (max-width: 1199px) and (min-width: 769px) {
        font-size: 3rem;
      }

      // Tablets
      @media (max-width: 768px) {
        font-size: 2.5rem;
      }

      // Mobile
      @media (max-width: 480px) {
        font-size: 2rem;
      }

      // Small mobile
      @media (max-width: 360px) {
        font-size: 1.5rem;
      }
    }

    .welcome {
      font-weight: 500;
      font-size: 3.5rem;
      margin: 0;
      line-height: 1.1;
      animation: fadeInUp 1s ease-out 0.2s both;
      color: rgba(255, 255, 255, 0.98);
      letter-spacing: 0.5px;

      // Large screens
      @media (min-width: 1200px) {
        font-size: 4rem;
      }

      // Medium screens
      @media (max-width: 1199px) and (min-width: 769px) {
        font-size: 3rem;
      }

      // Tablets
      @media (max-width: 768px) {
        font-size: 2.5rem;
      }

      // Mobile
      @media (max-width: 480px) {
        font-size: 2rem;
      }

      // Small mobile
      @media (max-width: 360px) {
        font-size: 1.5rem;
      }
    }
  }
}

// Right side - Responsive with scroll support for all content
.right-side {
  background: linear-gradient(135deg,
    #f1f5f9 0%,
    #e2e8f0 50%,
    #cbd5e1 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  position: relative;
  min-height: 100vh;
  overflow-y: auto;

  // Large screens - Fixed height, centered
  @media (min-width: 1200px) {
    padding: 2.5rem;
    min-height: 100vh;
    overflow-y: hidden;
  }

  // Medium screens - Fixed height, centered
  @media (max-width: 1199px) and (min-width: 769px) {
    padding: 2rem;
    min-height: 100vh;
    overflow-y: hidden;
  }

  // Tablets - Auto height with scroll
  @media (max-width: 768px) and (min-width: 481px) {
    padding: 1.5rem;
    min-height: auto;
    align-items: flex-start;
    padding-top: 2rem;
    padding-bottom: 2rem;
    overflow-y: auto;
  }

  // Mobile - Auto height with scroll
  @media (max-width: 480px) and (min-width: 361px) {
    padding: 1rem;
    min-height: auto;
    align-items: flex-start;
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
    overflow-y: auto;
  }

  // Small mobile - Auto height with scroll
  @media (max-width: 360px) {
    padding: 0.75rem;
    min-height: auto;
    align-items: flex-start;
    padding-top: 1rem;
    padding-bottom: 1rem;
    overflow-y: auto;
  }

  // Scroll indicator for mobile devices
  @media (max-width: 768px) {
    &::after {
      content: '';
      position: fixed;
      bottom: 10px;
      right: 10px;
      width: 4px;
      height: 40px;
      background: rgba(30, 64, 175, 0.3);
      border-radius: 2px;
      z-index: 1000;
      opacity: 0;
      animation: scrollHint 3s ease-in-out 1s infinite;
    }
  }

  .login-card {
    width: 100%;
    max-width: 420px;
    padding: 2.5rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow:
      0 8px 32px rgba(30, 58, 138, 0.12),
      0 4px 16px rgba(30, 64, 175, 0.08) !important;
    border-radius: 16px !important;
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: fadeInUp 0.6s ease-out;
    position: relative;
    z-index: 1;
    margin: auto;
    min-height: auto;

    // Large screens - Centered with fixed size
    @media (min-width: 1200px) {
      max-width: 450px;
      padding: 3rem;
    }

    // Medium screens - Centered with fixed size
    @media (max-width: 1199px) and (min-width: 769px) {
      max-width: 380px;
      padding: 2rem;
    }

    // Tablets - Full width with scroll support
    @media (max-width: 768px) and (min-width: 481px) {
      max-width: 100%;
      padding: 1.5rem;
      margin: 0;
      border-radius: 12px !important;
      min-height: auto;
    }

    // Mobile - Full width with scroll support
    @media (max-width: 480px) and (min-width: 361px) {
      max-width: 100%;
      padding: 1.25rem;
      border-radius: 8px !important;
      box-shadow:
        0 4px 16px rgba(30, 58, 138, 0.08),
        0 2px 8px rgba(30, 64, 175, 0.06) !important;
      margin: 0;
      min-height: auto;
    }

    // Small mobile - Full width with scroll support
    @media (max-width: 360px) {
      max-width: 100%;
      padding: 1rem;
      border-radius: 6px !important;
      box-shadow: 0 2px 8px rgba(30, 58, 138, 0.06) !important;
      margin: 0;
      min-height: auto;
    }

    // Simplified responsive company logo styling
    .logo-container {
      text-align: center;
      margin-bottom: 2rem;

      // Tablets
      @media (max-width: 768px) {
        margin-bottom: 1.5rem;
      }

      // Mobile
      @media (max-width: 480px) {
        margin-bottom: 1rem;
      }

      // Small mobile
      @media (max-width: 360px) {
        margin-bottom: 0.75rem;
      }

      .logo {
        width: 160px;
        height: auto;
        max-width: 100%;
        object-fit: contain;
        animation: fadeInScale 0.8s ease-out;
        transition: opacity 0.3s ease;

        // Large screens
        @media (min-width: 1200px) {
          width: 180px;
        }

        // Medium screens
        @media (max-width: 1199px) and (min-width: 769px) {
          width: 140px;
        }

        // Tablets
        @media (max-width: 768px) {
          width: 120px;
        }

        // Mobile
        @media (max-width: 480px) {
          width: 100px;
        }

        // Small mobile
        @media (max-width: 360px) {
          width: 90px;
        }

        &:hover {
          opacity: 0.9;
        }
      }
    }

    // Error message styling
    .error-message {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      color: #d32f2f;
      font-size: 0.9em;
      margin: 16px 0;
      padding: 12px;
      background-color: #ffebee;
      border: 1px solid #ffcdd2;
      border-radius: 8px;
      font-weight: 500;
      text-align: center;
      animation: shake 0.5s ease-in-out;

      mat-icon {
        color: #d32f2f;
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }

    // Simplified responsive login form styling
    .login-form {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;

      // Tablets
      @media (max-width: 768px) {
        gap: 1.25rem;
      }

      // Mobile
      @media (max-width: 480px) {
        gap: 1rem;
      }

      // Small mobile
      @media (max-width: 360px) {
        gap: 0.75rem;
      }

      .full-width {
        width: 100%;
      }

      // Form options (remember me)
      .form-options {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin: 0.5rem 0;

        // Mobile
        @media (max-width: 480px) {
          margin: 0.25rem 0;
        }

        // Small mobile
        @media (max-width: 360px) {
          margin: 0.125rem 0;
        }
      }

      // Angular Material button with custom theming
      .login-button {
        width: 100%;
        margin-top: 1.5rem;
        min-height: 48px;
        font-size: 1rem;
        font-weight: 500;
        background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #1d4ed8 100%) !important;
        color: white !important;
        border: none !important;
        box-shadow: 0 4px 14px rgba(30, 58, 138, 0.3) !important;

        // Large screens
        @media (min-width: 1200px) {
          font-size: 1.1rem;
          min-height: 52px;
        }

        // Medium screens
        @media (max-width: 1199px) and (min-width: 769px) {
          font-size: 1rem;
          min-height: 48px;
        }

        // Tablets
        @media (max-width: 768px) {
          margin-top: 1.25rem;
          min-height: 44px;
          font-size: 0.95rem;
        }

        // Mobile
        @media (max-width: 480px) {
          margin-top: 1rem;
          font-size: 0.9rem;
          min-height: 44px;
        }

        // Small mobile
        @media (max-width: 360px) {
          margin-top: 0.75rem;
          font-size: 0.85rem;
          min-height: 40px;
        }

        // Button states with gradient theme
        &:hover:not(:disabled) {
          background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 50%, #2563eb 100%) !important;
          box-shadow: 0 6px 20px rgba(30, 58, 138, 0.4) !important;
          transform: translateY(-1px);
        }

        &:active:not(:disabled) {
          background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #1e3a8a 100%) !important;
          box-shadow: 0 2px 8px rgba(30, 58, 138, 0.3) !important;
          transform: translateY(0);
        }

        &:disabled {
          background: linear-gradient(135deg, #9ca3af 0%, #6b7280 50%, #9ca3af 100%) !important;
          color: rgba(255, 255, 255, 0.7) !important;
          box-shadow: none !important;
          cursor: not-allowed;
        }

        mat-spinner {
          margin-right: 8px;

          // Small mobile
          @media (max-width: 360px) {
            margin-right: 6px;
          }
        }
      }
    }
  }
}

// Obvious and engaging animations
@keyframes obviousPulse {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

@keyframes floatObvious {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}

@keyframes spinObvious {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes bounceObvious {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes fadeObvious {
  0%, 100% {
    opacity: 0.4;
  }
  50% {
    opacity: 0.9;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}



@keyframes bounce {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes fade {
  0%, 100% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.6;
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes textGlow {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes cardFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes logoGlow {
  0%, 100% {
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
  }
  50% {
    filter: drop-shadow(0 6px 12px rgba(33, 150, 243, 0.2));
  }
}

@keyframes pulseGlow {
  0%, 100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

// Simplified responsive Material Design form field customization
::ng-deep .mat-mdc-form-field {
  margin-bottom: 1rem;

  // Tablets
  @media (max-width: 768px) {
    margin-bottom: 0.875rem;
  }

  // Mobile
  @media (max-width: 480px) {
    margin-bottom: 0.75rem;
  }

  // Small mobile
  @media (max-width: 360px) {
    margin-bottom: 0.625rem;
  }

  .mat-mdc-text-field-wrapper {
    .mat-mdc-form-field-flex {
      .mat-mdc-form-field-outline {
        .mat-mdc-form-field-outline-start,
        .mat-mdc-form-field-outline-notch,
        .mat-mdc-form-field-outline-end {
          border-color: rgba(0, 0, 0, 0.12);
          border-width: 1px;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
      }
    }
  }

  // Enhanced theming to complement the gradient design
  &.mat-focused {
    .mat-mdc-text-field-wrapper {
      .mat-mdc-form-field-flex {
        .mat-mdc-form-field-outline {
          .mat-mdc-form-field-outline-start,
          .mat-mdc-form-field-outline-notch,
          .mat-mdc-form-field-outline-end {
            border-color: #1e40af !important;
            border-width: 2px !important;
            box-shadow: 0 0 0 1px rgba(30, 64, 175, 0.1);
          }
        }
      }
    }
  }

  &.mat-focused .mat-mdc-form-field-label {
    color: #1e40af !important;
  }

  // Prefix and suffix icon theming
  .mat-mdc-form-field-icon-prefix {
    color: #64748b;
    transition: color 0.2s ease;
  }

  &.mat-focused .mat-mdc-form-field-icon-prefix {
    color: #1e40af;
  }

  .mat-mdc-form-field-icon-suffix {
    .mat-mdc-icon-button {
      color: #64748b;
      transition: all 0.2s ease;

      &:hover {
        color: #1e40af;
        background-color: rgba(30, 64, 175, 0.05);
      }
    }
  }
}

// Enhanced checkbox theming to match gradient design
::ng-deep .mat-mdc-checkbox {
  .mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background {
    background-color: #1e40af !important;
    border-color: #1e40af !important;
  }

  .mdc-checkbox__native-control:enabled:focus ~ .mdc-checkbox__background {
    border-color: #1e40af !important;
  }

  &:hover .mdc-checkbox__native-control:enabled ~ .mdc-checkbox__background {
    border-color: #64748b;
  }

  .mdc-checkbox__ripple {
    background-color: rgba(30, 64, 175, 0.05);
  }
}

// Scroll hint animation for mobile devices
@keyframes scrollHint {
  0%, 100% {
    opacity: 0;
    transform: translateY(0);
  }
  50% {
    opacity: 0.6;
    transform: translateY(10px);
  }
}

// Touch-friendly improvements for mobile
@media (max-width: 768px) {
  // Ensure minimum touch target sizes
  ::ng-deep .mat-mdc-form-field {
    .mat-mdc-text-field-wrapper {
      min-height: 48px;
    }
  }

  ::ng-deep .mat-mdc-icon-button {
    min-width: 44px;
    min-height: 44px;
  }

  ::ng-deep .mat-mdc-checkbox {
    .mdc-checkbox {
      width: 24px;
      height: 24px;
    }
  }
}

// Ensure content is always visible with proper spacing
@media (max-width: 768px) {
  .login-container {
    padding-bottom: env(safe-area-inset-bottom, 0);
  }
}

// Custom scrollbar styling for better UX
@media (min-width: 769px) {
  .right-side {
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(241, 245, 249, 0.5);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(30, 64, 175, 0.3);
      border-radius: 3px;

      &:hover {
        background: rgba(30, 64, 175, 0.5);
      }
    }
  }
}

// Hide browser's default password visibility toggle
.no-browser-password-toggle {
  // Hide Edge/IE password reveal button
  &::-ms-reveal,
  &::-ms-clear {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
  }

  // Hide Chrome/Safari password reveal button
  &::-webkit-credentials-auto-fill-button,
  &::-webkit-strong-password-auto-fill-button {
    display: none !important;
    visibility: hidden !important;
    pointer-events: none !important;
    position: absolute !important;
    right: 0 !important;
  }

  // Additional browser-specific hiding
  &[type="password"]::-webkit-textfield-decoration-container {
    visibility: hidden !important;
  }

  // Firefox password reveal button (if any)
  &::-moz-reveal {
    display: none !important;
  }
}