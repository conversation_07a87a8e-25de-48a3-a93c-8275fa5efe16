<div class="submenu-item" [style.paddingLeft.px]="level * 20">
  <p class="compact-submenu-item" [ngClass]="'level-' + level" (click)="toggleSubmenu($event)">
    <span>
      <mat-icon>{{ getIcon(menuItem.type) }}</mat-icon>
      {{ menuItem.description ||  menuItem.application}}
    </span>
    @if (menuItem.type === 'menu') {
      <mat-icon class="submenu-arrow" [class.rotated]="expanded">
        expand_more
      </mat-icon>
    }
  </p>

  @if (loading) {
    <div>Loading...</div>
  }

  @if (expanded && !loading) {
    <div>
      @for (submenu of submenuItems; track submenu) {
        <app-submenu
          [menuItem]="submenu"
          [level]="level + 1"
          (onMenuSelected)="selectMenuItem($event)">
        </app-submenu>
      }
    </div>
  }
</div>
