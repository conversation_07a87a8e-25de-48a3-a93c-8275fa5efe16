import { Component,Input, Output, EventEmitter, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MetadataService } from '../services/metadata.service';


@Component({
  selector: 'app-submenu',
  imports: [ MatIconModule,
    CommonModule],
  templateUrl: './submenu.component.html',
  styleUrl: './submenu.component.scss'
})
export class SubmenuComponent {
  @Input() menuItem: any;
  @Input() level: number = 1;
  @Output() onMenuSelected = new EventEmitter<any>();

  submenuItems: any[] = [];
  expanded = false;
  loaded = false;
  loading = false;
  private metadataService = inject(MetadataService);
  constructor(){}

   toggleSubmenu(event: Event) {
    event.stopPropagation(); // Stop event propagation to avoid triggering parent actions
    if (this.menuItem.type === 'menu') { // Only toggle if it's a menu type
      this.expanded = !this.expanded;

      if (this.expanded && !this.loaded) {
        this.loadSubmenu();
      }
    } else {
      this.selectMenuItem(this.menuItem); // Call selectMenuItem if it's not a 'menu' type
    }
  }

  // Helper function to extract API ID from application string
  private extractApiId(application: string): string {
    if (application && application.includes(',')) {
      return application.split(',')[0].trim();
    }
    return application;
  }

  loadSubmenu() {
    this.loading = true;
    
    // Use extracted API ID for API calls
    const apiId = this.extractApiId(this.menuItem.application);
    
    this.metadataService.getMenu(apiId).subscribe(
      (response) => {
        this.submenuItems = response?.menus || [];
        this.loaded = true;
        this.loading = false;
      },
      (error) => {
        console.error(`Error loading submenu for ${apiId}:`, error);
        this.loading = false;
      }
    );
  }

  selectMenuItem(menuItem: any) {
    this.onMenuSelected.emit(menuItem); // Emit the selected menu item for all types
  }

  getIcon(menuType: string): string {
    switch (menuType) {
      case 'menu':
        return 'list';
      case 'qur':
        return 'query_stats';
      case 'scr':
        return 'screen_share';
      case 'table':
        return 'table_chart';
      default:
        return 'info';
    }
  }

}
