import { Component, Input, Output, EventEmitter, On<PERSON><PERSON>roy, OnInit, OnChanges, inject, ChangeDetectorRef } from '@angular/core';
import { FormGroup, FormArray } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { DropdownComponent, DropdownConfig, DropdownValueChangeEvent } from '../dropdown/dropdown.component';

@Component({
  selector: 'app-regular-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatTooltipModule,
    DropdownComponent
  ],
  templateUrl: './regular-field.component.html',
  styleUrl: './regular-field.component.scss'
})
export class RegularFieldComponent implements OnInit, OnDestroy, OnChanges {
  @Input() field!: any;
  @Input() form!: FormGroup;
  @Input() isViewMode: boolean = false;
  @Input() fields: any[] = []; // Need access to all fields for extractOriginalFieldName
  @Input() multiIndex?: number; // Optional index for multi-fields (1-based)
  @Input() groupIndex?: number; // Optional group index for grouped fields
  @Input() nestedGroupIndex?: number; // Optional nested group index for nested groups

  @Output() fieldValueChange = new EventEmitter<{fieldName: string, value: any}>();

  // Note: Dropdown properties moved to unified DropdownComponent
  // API caching is now handled by the unified DropdownComponent

  private cdr = inject(ChangeDetectorRef);

  ngOnInit() {
    // Dropdown preloading is now handled by the unified DropdownComponent
    // Ensure form control is properly disabled when it should be
    this.updateFormControlDisabledState();
  }

  private updateFormControlDisabledState(): void {
    const formControl = this.form.get(this.field.fieldName);
    if (formControl) {
      if (this.isViewMode || this.field.noInput) {
        if (formControl.enabled) {
          formControl.disable();
        }
      } else {
        if (formControl.disabled) {
          formControl.enable();
        }
      }
    }
  }

  ngOnChanges(): void {
    // Update form control disabled state when inputs change
    this.updateFormControlDisabledState();
  }

  ngOnDestroy() {
    // Cleanup handled by unified DropdownComponent
  }

  // Note: All dropdown methods moved to unified DropdownComponent
  // Utility methods are now handled by the unified DropdownComponent

  // Note: Group path parsing and array access handled by parent component

  // Dropdown preloading is now handled by the unified DropdownComponent

  // Configuration methods for unified dropdown component
  getTypeDropdownConfig(field: any): DropdownConfig {
    return {
      type: 'type',
      queryBuilderId: 'fieldType',
      searchEnabled: true,
      placeholder: `Search ${field.label?.trim() || field.fieldName}`,
      emptyMessage: 'No types found',
      tooltip: 'Show type suggestions'
    };
  }

  getForeignKeyDropdownConfig(field: any): DropdownConfig {
    return {
      type: 'foreignKey',
      queryBuilderId: 'formDefinition',
      searchEnabled: true,
      placeholder: `Search ${field.label?.trim() || field.fieldName}`,
      emptyMessage: 'No foreign keys found',
      tooltip: 'Show foreign key suggestions'
    };
  }

  getRegularDropdownConfig(field: any): DropdownConfig {
    return {
      type: 'regular',
      queryBuilderId: field.foreginKey,
      searchEnabled: true,
      placeholder: `Search ${field.label?.trim() || field.fieldName}`,
      emptyMessage: 'No options found',
      tooltip: 'Show options'
    };
  }

  getLookupDropdownConfig(field: any): DropdownConfig {
    return {
      type: 'lookup',
      searchEnabled: true,
      placeholder: `Select ${field.label?.trim() || field.fieldName}`,
      emptyMessage: 'No options found',
      tooltip: 'Show lookup options'
    };
  }

  // Event handler for unified dropdown component
  onDropdownValueChange(event: DropdownValueChangeEvent): void {
    // Map the unique ID back to the original field name
    const originalFieldName = this.extractOriginalFieldName(event.fieldName);
    
    // Emit the field value change event for parent component
    this.fieldValueChange.emit({
      fieldName: originalFieldName,
      value: event.value
    });
  }

  // Extract original field name from unique ID
  private extractOriginalFieldName(uniqueId: string): string {
    // Handle complex unique IDs with group, nested, and multi indices
    if (uniqueId.includes('_group_') || uniqueId.includes('_nested_') || uniqueId.includes('_multi_')) {
      // Split by underscores and take the first part (original field name)
      const parts = uniqueId.split('_');
      return parts[0];
    }
    return uniqueId;
  }

  // Helper method to get FormControl with proper typing
  getFormControl(fieldName: string): any {
    return this.form.get(fieldName);
  }

  // Generate unique ID for dropdown fields to prevent conflicts in multi-field scenarios
  getUniqueFieldId(fieldName: string): string {
    let uniqueId = fieldName;
    
    // Add group index if available
    if (this.groupIndex !== undefined) {
      uniqueId += `_group_${this.groupIndex}`;
    }
    
    // Add nested group index if available
    if (this.nestedGroupIndex !== undefined) {
      uniqueId += `_nested_${this.nestedGroupIndex}`;
    }
    
    // Add multi index if available
    if (this.multiIndex) {
      uniqueId += `_multi_${this.multiIndex}`;
    }
    
    return uniqueId;
  }
}
